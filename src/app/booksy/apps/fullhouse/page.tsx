"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useDataSync } from "../../lib/dataSync";
import { booksyApi } from "../../lib/apiClient";
import {
  Home,
  Wrench,
  Utensils,
  Car,
  ShoppingCart,
  Heart,
  Shield,
  Zap,
  Droplets,
  Wifi,
  Thermometer,
  Camera,
  Baby,
  GraduationCap,
  Gamepad2,
  Music,
  Dumbbell,
  Scissors,
  Truck,
  Package,
  Clock,
  MapPin,
  Star,
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Bell,
  User,
  Settings,
  Calendar,
  CreditCard,
  MessageCircle,
  ArrowRight,
  Percent,
  Timer,
  Users,
  CheckCircle,
  AlertTriangle,
  UtensilsCrossed
} from "lucide-react";

interface ServiceCategory {
  id: string;
  name: string;
  icon: string;
  description: string;
  color: string;
  services: Service[];
}

interface Service {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  price?: string;
  rating?: number;
  isPopular?: boolean;
  isEmergency?: boolean;
  estimatedTime?: string;
}

interface QuickAction {
  id: string;
  name: string;
  icon: string;
  color: string;
  action: string;
}

interface FlashDeal {
  id: string;
  providerId: string;
  providerName: string;
  title: string;
  description: string;
  serviceName: string;
  originalPrice: number;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  finalPrice: number;
  endDate: string;
  maxBookings: number;
  currentBookings: number;
  status: 'active' | 'ending_soon';
  targetAudience: string;
  areas: string[];
}

const iconMap: { [key: string]: any } = {
  Home, Wrench, Utensils, Car, ShoppingCart, Heart, Shield, Zap,
  Droplets, Wifi, Thermometer, Camera, Baby, GraduationCap, Gamepad2,
  Music, Dumbbell, Scissors, Truck, Package, Clock, MapPin, Star,
  Plus, Search, Filter, Grid, List, Bell, User, Settings, Calendar,
  CreditCard, MessageCircle, ArrowRight, Percent, Timer, Users, UtensilsCrossed
};

const DynamicIcon = ({ iconName, size = 24, className = "" }: { iconName: string, size?: number, className?: string }) => {
  const IconComponent = iconMap[iconName];
  return IconComponent ? <IconComponent size={size} className={className} /> : <Home size={size} className={className} />;
};

export default function FullHousePage() {
  const router = useRouter();
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [quickActions, setQuickActions] = useState<QuickAction[]>([]);
  const [flashDeals, setFlashDeals] = useState<FlashDeal[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showEmergencyOnly, setShowEmergencyOnly] = useState(false);
  const [pendingConfirmations, setPendingConfirmations] = useState<any[]>([]);
  const [recentOrders, setRecentOrders] = useState<any[]>([]);

  // Subscribe to flash deals changes for real-time updates
  const flashDealChange = useDataSync('flash-deal');

  useEffect(() => {
    // Load categories and services
    fetch("/api/booksy/fullhouse/categories")
      .then(res => res.json())
      .then(data => setCategories(data))
      .catch(error => console.error("Error loading categories:", error));

    // Load quick actions
    fetch("/api/booksy/fullhouse/quick-actions")
      .then(res => res.json())
      .then(data => setQuickActions(data))
      .catch(error => console.error("Error loading quick actions:", error));

    // Load flash deals
    loadFlashDeals();

    // Load pending confirmations and recent orders
    loadPendingConfirmations();
    loadRecentOrders();
  }, []);

  // Reload flash deals when they change
  useEffect(() => {
    if (flashDealChange) {
      loadFlashDeals();
    }
  }, [flashDealChange]);

  const loadFlashDeals = async () => {
    try {
      const response = await booksyApi.getFlashDeals({ status: 'active', limit: 10 });
      if (response.success) {
        setFlashDeals(response.data.deals || []);
      } else {
        console.error('Error loading flash deals:', response.error);
        setFlashDeals([]);
      }
    } catch (error) {
      console.error('Error loading flash deals:', error);
      setFlashDeals([]);
    }
  };

  const loadPendingConfirmations = async () => {
    try {
      const response = await fetch('/api/booksy/completion-confirmations?status=requested&confirmationType=customer');
      const data = await response.json();
      setPendingConfirmations(data.items || []);
    } catch (error) {
      console.error('Error loading pending confirmations:', error);
    }
  };

  const loadRecentOrders = async () => {
    try {
      // Load recent orders for current customer
      const response = await fetch('/api/booksy/providers/orders?limit=5');
      const data = await response.json();
      setRecentOrders(data.orders || []);
    } catch (error) {
      console.error('Error loading recent orders:', error);
    }
  };

  const handleConfirmCompletion = async (itemId: string, itemType: string, action: 'confirm' | 'reject', notes?: string, rejectionReason?: string) => {
    try {
      const response = await fetch('/api/booksy/completion-confirmations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: itemType,
          id: itemId,
          action,
          confirmedBy: 'current-customer', // This would come from auth context
          confirmationType: 'customer',
          notes,
          rejectionReason
        }),
      });

      if (response.ok) {
        loadPendingConfirmations(); // Refresh the list
        loadRecentOrders(); // Refresh recent orders
      }
    } catch (error) {
      console.error('Error handling completion confirmation:', error);
    }
  };

  const filteredServices = categories.flatMap(cat =>
    cat.services.filter(service => {
      const matchesSearch = service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           service.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = selectedCategory === "all" || service.category === selectedCategory;
      const matchesEmergency = !showEmergencyOnly || service.isEmergency;
      
      return matchesSearch && matchesCategory && matchesEmergency;
    })
  );

  const handleServiceClick = (service: Service) => {
    // Handle food orders category specially
    if (service.category === 'food-orders') {
      router.push('/booksy/apps/fullhouse/food');
    } else {
      router.push(`/booksy/apps/fullhouse/service/${service.id}`);
    }
  };

  const handleQuickAction = (action: QuickAction) => {
    switch (action.action) {
      case "emergency":
        setShowEmergencyOnly(true);
        setSelectedCategory("all");
        break;
      case "popular":
        // Filter popular services
        break;
      case "recent":
        router.push("/booksy/apps/fullhouse/recent");
        break;
      case "favorites":
        router.push("/booksy/apps/fullhouse/favorites");
        break;
      default:
        break;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Home className="h-8 w-8 text-blue-600" />
                <h1 className="text-2xl font-bold text-gray-900">Full House</h1>
              </div>
              <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                Times City, Hà Nội
              </span>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Bell size={20} />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <User size={20} />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Settings size={20} />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Thao Tác Nhanh</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {quickActions.map((action) => (
              <button
                key={action.id}
                onClick={() => handleQuickAction(action)}
                className={`p-4 rounded-lg border-2 border-dashed transition-all hover:scale-105 ${action.color}`}
              >
                <DynamicIcon iconName={action.icon} size={32} className="mx-auto mb-2" />
                <span className="text-sm font-medium block">{action.name}</span>
              </button>
            ))}
          </div>

          {/* Work Pool Quick Access */}
          <div className="mt-6">
            <Link
              href="/booksy/apps/fullhouse/workpool"
              className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white hover:from-blue-600 hover:to-purple-700 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white bg-opacity-20 rounded-lg">
                  <Package className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="font-semibold">Kho Công Việc</h3>
                  <p className="text-sm opacity-90">Đăng công việc và nhận đấu thầu từ nhiều nhà cung cấp</p>
                </div>
              </div>
              <ArrowRight className="h-5 w-5" />
            </Link>
          </div>
        </div>

        {/* Flash Deals Section */}
        {flashDeals.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <Zap className="h-5 w-5 text-yellow-500 mr-2" />
                Flash Deals Hôm Nay
              </h2>
              <Link
                href="/booksy/stores"
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
              >
                Xem tất cả
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {flashDeals.slice(0, 2).map((deal) => {
                const timeLeft = new Date(deal.endDate).getTime() - new Date().getTime();
                const hoursLeft = Math.max(0, Math.floor(timeLeft / (1000 * 60 * 60)));
                const isEndingSoon = hoursLeft <= 24;

                return (
                  <div
                    key={deal.id}
                    className={`bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg p-6 text-white relative overflow-hidden ${
                      isEndingSoon ? 'animate-pulse' : ''
                    }`}
                  >
                    {/* Background Pattern */}
                    <div className="absolute inset-0 opacity-10">
                      <div className="absolute top-4 right-4">
                        <Zap className="h-16 w-16" />
                      </div>
                    </div>

                    <div className="relative z-10">
                      {/* Deal Badge */}
                      <div className="flex items-center justify-between mb-3">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-white bg-opacity-20">
                          <Zap className="w-3 h-3 mr-1" />
                          {isEndingSoon ? 'SẮP HẾT HẠN' : 'FLASH DEAL'}
                        </span>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-400 text-yellow-900">
                          -{deal.discountType === 'percentage' ? `${deal.discountValue}%` : `${(deal.discountValue / 1000).toFixed(0)}K`}
                        </span>
                      </div>

                      {/* Deal Content */}
                      <h3 className="text-lg font-bold mb-2 line-clamp-2">
                        {deal.title}
                      </h3>

                      <p className="text-sm text-pink-100 mb-3 line-clamp-2">
                        {deal.description}
                      </p>

                      {/* Pricing */}
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-2xl font-bold">
                          {new Intl.NumberFormat('vi-VN', {
                            style: 'currency',
                            currency: 'VND',
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                          }).format(deal.finalPrice)}
                        </span>
                        <span className="text-lg text-pink-200 line-through">
                          {new Intl.NumberFormat('vi-VN', {
                            style: 'currency',
                            currency: 'VND',
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                          }).format(deal.originalPrice)}
                        </span>
                      </div>

                      {/* Progress and Time */}
                      <div className="flex items-center justify-between text-sm mb-4">
                        <div className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          <span>{deal.currentBookings}/{deal.maxBookings} đã đặt</span>
                        </div>
                        <div className="flex items-center">
                          <Timer className="w-4 h-4 mr-1" />
                          <span>
                            {hoursLeft > 0 ? `Còn ${hoursLeft}h` : 'Đã hết hạn'}
                          </span>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-white bg-opacity-20 rounded-full h-2 mb-4">
                        <div
                          className="bg-white h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(deal.currentBookings / deal.maxBookings) * 100}%` }}
                        ></div>
                      </div>

                      {/* Action Button */}
                      <button
                        className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                          deal.currentBookings >= deal.maxBookings
                            ? 'bg-gray-400 cursor-not-allowed text-gray-200'
                            : 'bg-white text-pink-600 hover:bg-pink-50'
                        }`}
                        disabled={deal.currentBookings >= deal.maxBookings}
                      >
                        {deal.currentBookings >= deal.maxBookings ? 'Đã hết slot' : 'Đặt ngay'}
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Pending Completion Confirmations */}
        {pendingConfirmations.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900 flex items-center">
                <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
                Cần Xác Nhận Hoàn Thành
              </h2>
              <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                {pendingConfirmations.length} việc
              </span>
            </div>

            <div className="space-y-4">
              {pendingConfirmations.map((item) => (
                <div key={`${item.type}-${item.id}`} className="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        {item.type === 'order' ? (
                          <Package className="h-4 w-4 text-blue-500" />
                        ) : (
                          <MessageCircle className="h-4 w-4 text-green-500" />
                        )}
                        <h3 className="text-sm font-medium text-gray-900">
                          {item.title || item.serviceInfo?.name || 'Dịch vụ'}
                        </h3>
                        <span className="text-xs text-gray-500">
                          #{item.id}
                        </span>
                      </div>

                      <p className="text-sm text-gray-600 mb-3">
                        Nhà cung cấp đã hoàn thành công việc và yêu cầu xác nhận từ bạn.
                      </p>

                      {item.completionConfirmation?.requestedAt && (
                        <p className="text-xs text-gray-500 mb-3">
                          Yêu cầu lúc: {new Date(item.completionConfirmation.requestedAt).toLocaleString('vi-VN')}
                        </p>
                      )}

                      <div className="flex space-x-3">
                        <button
                          onClick={() => handleConfirmCompletion(item.id, item.type, 'confirm', 'Công việc đã được hoàn thành tốt.')}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Xác nhận hoàn thành
                        </button>
                        <button
                          onClick={() => handleConfirmCompletion(item.id, item.type, 'reject', '', 'Công việc chưa hoàn thành đúng yêu cầu')}
                          className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <AlertTriangle className="h-4 w-4 mr-1" />
                          Cần làm lại
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recent Orders Status */}
        {recentOrders.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900 flex items-center">
                <Clock className="h-5 w-5 text-blue-500 mr-2" />
                Đơn Hàng Gần Đây
              </h2>
              <Link
                href="/booksy/apps/fullhouse/orders"
                className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
              >
                Xem tất cả
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>

            <div className="space-y-3">
              {recentOrders.slice(0, 3).map((order) => (
                <div key={order.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Package className="h-5 w-5 text-gray-400" />
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {order.serviceInfo?.name || 'Dịch vụ'}
                      </h4>
                      <p className="text-xs text-gray-500">
                        {new Date(order.createdAt).toLocaleDateString('vi-VN')}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      order.status === 'completed' ? 'bg-green-100 text-green-800' :
                      order.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                      order.status === 'confirmed' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {order.status === 'completed' && 'Hoàn thành'}
                      {order.status === 'in_progress' && 'Đang thực hiện'}
                      {order.status === 'confirmed' && 'Đã xác nhận'}
                      {order.status === 'pending' && 'Chờ xác nhận'}
                    </span>

                    {order.completionConfirmation?.status === 'confirmed' && (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Work Pool Integration */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-900 flex items-center">
              <Users className="h-5 w-5 text-purple-500 mr-2" />
              Đăng Công Việc Lên Work Pool
            </h2>
          </div>

          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Plus className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Cần dịch vụ đặc biệt?
                </h3>
                <p className="text-gray-600 mb-4">
                  Đăng công việc lên Work Pool để nhận đấu thầu từ nhiều nhà cung cấp.
                  So sánh giá cả và chọn người phù hợp nhất.
                </p>
                <Link
                  href="/booksy/apps/workpool"
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Đăng công việc mới
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Tìm kiếm dịch vụ gia đình..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <select
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="all">Tất Cả Danh Mục</option>
                {categories.map((cat) => (
                  <option key={cat.id} value={cat.id}>{cat.name}</option>
                ))}
              </select>
              
              <button
                onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                <DynamicIcon iconName={viewMode === "grid" ? "List" : "Grid"} size={20} />
              </button>
              
              <button
                onClick={() => setShowEmergencyOnly(!showEmergencyOnly)}
                className={`px-4 py-2 rounded-lg border ${showEmergencyOnly ? 'bg-red-100 border-red-300 text-red-700' : 'border-gray-300 hover:bg-gray-50'}`}
              >
                Chỉ Khẩn Cấp
              </button>
            </div>
          </div>
        </div>

        {/* Services Grid/List */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Dịch Vụ Có Sẵn ({filteredServices.length})
            </h2>
          </div>
          
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredServices.map((service) => (
                <div
                  key={service.id}
                  onClick={() => handleServiceClick(service)}
                  className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer p-6"
                >
                  <div className="flex items-center justify-between mb-4">
                    <DynamicIcon iconName={service.icon} size={32} className="text-blue-600" />
                    {service.isEmergency && (
                      <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                        Khẩn Cấp
                      </span>
                    )}
                    {service.isPopular && (
                      <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                        Phổ Biến
                      </span>
                    )}
                  </div>
                  
                  <h3 className="font-semibold text-gray-900 mb-2">{service.name}</h3>
                  <p className="text-sm text-gray-600 mb-4">{service.description}</p>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">{service.estimatedTime}</span>
                    {service.price && (
                      <span className="font-semibold text-blue-600">{service.price}</span>
                    )}
                  </div>
                  
                  {service.rating && (
                    <div className="flex items-center mt-2">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600 ml-1">{service.rating}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredServices.map((service) => (
                <div
                  key={service.id}
                  onClick={() => handleServiceClick(service)}
                  className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer p-6"
                >
                  <div className="flex items-center space-x-4">
                    <DynamicIcon iconName={service.icon} size={40} className="text-blue-600" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-gray-900">{service.name}</h3>
                        <div className="flex items-center space-x-2">
                          {service.isEmergency && (
                            <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                              Khẩn Cấp
                            </span>
                          )}
                          {service.isPopular && (
                            <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                              Phổ Biến
                            </span>
                          )}
                          {service.price && (
                            <span className="font-semibold text-blue-600">{service.price}</span>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-sm text-gray-500">{service.estimatedTime}</span>
                        {service.rating && (
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <span className="text-sm text-gray-600 ml-1">{service.rating}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
