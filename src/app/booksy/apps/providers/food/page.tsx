'use client';

import { useState, useEffect } from 'react';
import { 
  Store, 
  Plus, 
  Settings, 
  Eye, 
  Edit,
  Trash2,
  Clock,
  DollarSign,
  Star,
  Package,
  ChefHat,
  Utensils,
  Coffee,
  Cake,
  ArrowLeft,
  ToggleLeft,
  ToggleRight,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Link from 'next/link';

interface MenuItem {
  id: string;
  restaurantId: string;
  name: string;
  description: string;
  category: string;
  subcategory: string;
  price: {
    basePrice: number;
    currency: string;
  };
  variants: Array<{
    id: string;
    name: string;
    price: number;
  }>;
  images: Array<{
    url: string;
    alt: string;
    isPrimary: boolean;
  }>;
  availability: {
    isAvailable: boolean;
    availableFrom: string;
    availableTo: string;
  };
  preparationTime: {
    min: number;
    max: number;
  };
  rating: number;
  totalReviews: number;
  orderCount: number;
  status: 'active' | 'inactive' | 'out_of_stock';
  isSignature: boolean;
  isPopular: boolean;
  isFeatured: boolean;
  tags: string[];
}

interface Restaurant {
  id: string;
  name: string;
  description: string;
  cuisineType: string[];
  category: string;
  rating: number;
  totalReviews: number;
  status: 'active' | 'inactive';
  operatingHours: Record<string, { open: string; close: string; isOpen: boolean }>;
  deliveryInfo: {
    isDeliveryAvailable: boolean;
    deliveryFee: number;
    estimatedDeliveryTime: { min: number; max: number };
  };
}

const FoodManagementPage = () => {
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('menu');
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Mock data for development
        setRestaurant({
          id: 'restaurant-001',
          name: 'Phở Hà Nội Truyền Thống',
          description: 'Phở Hà Nội chính hiệu với nước dùng niêu trong suốt 24 giờ',
          cuisineType: ['vietnamese', 'noodles'],
          category: 'restaurant',
          rating: 4.8,
          totalReviews: 324,
          status: 'active',
          operatingHours: {
            monday: { open: '06:00', close: '22:00', isOpen: true },
            tuesday: { open: '06:00', close: '22:00', isOpen: true },
            wednesday: { open: '06:00', close: '22:00', isOpen: true },
            thursday: { open: '06:00', close: '22:00', isOpen: true },
            friday: { open: '06:00', close: '22:00', isOpen: true },
            saturday: { open: '06:00', close: '23:00', isOpen: true },
            sunday: { open: '06:00', close: '23:00', isOpen: true }
          },
          deliveryInfo: {
            isDeliveryAvailable: true,
            deliveryFee: 15000,
            estimatedDeliveryTime: { min: 20, max: 35 }
          }
        });

        setMenuItems([
          {
            id: 'menu-item-001',
            restaurantId: 'restaurant-001',
            name: 'Phở Bò Tái',
            description: 'Phở bò với thịt bò tái, nước dùng trong suốt niêu 24 giờ',
            category: 'main_course',
            subcategory: 'noodles',
            price: { basePrice: 65000, currency: 'VND' },
            variants: [
              { id: 'variant-001', name: 'Size Nhỏ', price: 65000 },
              { id: 'variant-002', name: 'Size Lớn', price: 85000 }
            ],
            images: [
              { url: '/images/menu/pho-bo-tai-1.jpg', alt: 'Phở Bò Tái', isPrimary: true }
            ],
            availability: {
              isAvailable: true,
              availableFrom: '06:00',
              availableTo: '22:00'
            },
            preparationTime: { min: 8, max: 12 },
            rating: 4.9,
            totalReviews: 156,
            orderCount: 2847,
            status: 'active',
            isSignature: true,
            isPopular: true,
            isFeatured: true,
            tags: ['signature', 'traditional', 'halal', 'popular']
          },
          {
            id: 'menu-item-002',
            restaurantId: 'restaurant-001',
            name: 'Phở Gà',
            description: 'Phở gà với thịt gà luộc, nước dùng ngọt thanh',
            category: 'main_course',
            subcategory: 'noodles',
            price: { basePrice: 60000, currency: 'VND' },
            variants: [
              { id: 'variant-003', name: 'Size Nhỏ', price: 60000 },
              { id: 'variant-004', name: 'Size Lớn', price: 80000 }
            ],
            images: [
              { url: '/images/menu/pho-ga-1.jpg', alt: 'Phở Gà', isPrimary: true }
            ],
            availability: {
              isAvailable: true,
              availableFrom: '06:00',
              availableTo: '22:00'
            },
            preparationTime: { min: 8, max: 12 },
            rating: 4.7,
            totalReviews: 89,
            orderCount: 1456,
            status: 'active',
            isSignature: false,
            isPopular: true,
            isFeatured: false,
            tags: ['traditional', 'halal', 'popular']
          }
        ]);
      } catch (error) {
        console.error('Failed to fetch food data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const toggleItemAvailability = (itemId: string) => {
    setMenuItems(items => 
      items.map(item => 
        item.id === itemId 
          ? { ...item, availability: { ...item.availability, isAvailable: !item.availability.isAvailable } }
          : item
      )
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'inactive':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'out_of_stock':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'main_course':
        return <Utensils className="w-4 h-4" />;
      case 'appetizer':
        return <ChefHat className="w-4 h-4" />;
      case 'beverage':
        return <Coffee className="w-4 h-4" />;
      case 'dessert':
        return <Cake className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  const filteredMenuItems = selectedCategory === 'all' 
    ? menuItems 
    : menuItems.filter(item => item.category === selectedCategory);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/booksy/apps/providers" className="text-gray-500 hover:text-gray-700">
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <Store className="w-6 h-6 text-orange-500" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Quản Lý Thực Đơn</h1>
                <p className="text-sm text-gray-500">{restaurant?.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Link
                href="/booksy/apps/providers/food/dashboard"
                className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center space-x-2"
              >
                <BarChart3 className="w-4 h-4" />
                <span>Dashboard</span>
              </Link>
              <Link
                href="/booksy/apps/providers/food/add"
                className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Thêm Món</span>
              </Link>
              <Link
                href="/booksy/apps/providers/food/settings"
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2"
              >
                <Settings className="w-4 h-4" />
                <span>Cài Đặt</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Restaurant Status */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Store className="w-6 h-6 text-orange-500" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">{restaurant?.name}</h2>
                <p className="text-sm text-gray-500">{restaurant?.description}</p>
                <div className="flex items-center space-x-4 mt-2">
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm font-medium">{restaurant?.rating}</span>
                    <span className="text-sm text-gray-500">({restaurant?.totalReviews} đánh giá)</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(restaurant?.status || 'inactive')}
                    <span className="text-sm text-gray-600 capitalize">{restaurant?.status}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">{menuItems.length}</div>
              <div className="text-sm text-gray-500">Món ăn</div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('menu')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'menu'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Thực Đơn
              </button>
              <button
                onClick={() => setActiveTab('orders')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'orders'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Đơn Hàng
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'analytics'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Thống Kê
              </button>
            </nav>
          </div>

          {/* Menu Tab Content */}
          {activeTab === 'menu' && (
            <div className="p-6">
              {/* Category Filter */}
              <div className="flex items-center space-x-4 mb-6">
                <span className="text-sm font-medium text-gray-700">Danh mục:</span>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setSelectedCategory('all')}
                    className={`px-3 py-1 rounded-full text-sm ${
                      selectedCategory === 'all'
                        ? 'bg-orange-100 text-orange-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    Tất cả
                  </button>
                  <button
                    onClick={() => setSelectedCategory('main_course')}
                    className={`px-3 py-1 rounded-full text-sm ${
                      selectedCategory === 'main_course'
                        ? 'bg-orange-100 text-orange-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    Món chính
                  </button>
                  <button
                    onClick={() => setSelectedCategory('appetizer')}
                    className={`px-3 py-1 rounded-full text-sm ${
                      selectedCategory === 'appetizer'
                        ? 'bg-orange-100 text-orange-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    Khai vị
                  </button>
                  <button
                    onClick={() => setSelectedCategory('beverage')}
                    className={`px-3 py-1 rounded-full text-sm ${
                      selectedCategory === 'beverage'
                        ? 'bg-orange-100 text-orange-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    Đồ uống
                  </button>
                </div>
              </div>

              {/* Menu Items Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredMenuItems.map((item) => (
                  <div key={item.id} className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        {getCategoryIcon(item.category)}
                        <span className="text-sm text-gray-600 capitalize">{item.subcategory}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(item.status)}
                        <button
                          onClick={() => toggleItemAvailability(item.id)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          {item.availability.isAvailable ? (
                            <ToggleRight className="w-5 h-5 text-green-500" />
                          ) : (
                            <ToggleLeft className="w-5 h-5 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </div>

                    <h3 className="font-semibold text-gray-900 mb-2">{item.name}</h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">{item.description}</p>

                    <div className="flex items-center justify-between mb-3">
                      <div className="text-lg font-bold text-orange-600">
                        {item.price.basePrice.toLocaleString('vi-VN')}đ
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm font-medium">{item.rating}</span>
                        <span className="text-sm text-gray-500">({item.totalReviews})</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{item.preparationTime.min}-{item.preparationTime.max} phút</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Package className="w-4 h-4" />
                        <span>{item.orderCount} đơn</span>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Link
                        href={`/booksy/apps/providers/food/edit/${item.id}`}
                        className="flex-1 bg-orange-500 text-white text-center py-2 rounded-lg hover:bg-orange-600 flex items-center justify-center space-x-1"
                      >
                        <Edit className="w-4 h-4" />
                        <span>Sửa</span>
                      </Link>
                      <Link
                        href={`/booksy/apps/providers/food/view/${item.id}`}
                        className="flex-1 bg-gray-100 text-gray-700 text-center py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center space-x-1"
                      >
                        <Eye className="w-4 h-4" />
                        <span>Xem</span>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Orders Tab Content */}
          {activeTab === 'orders' && (
            <div className="p-6">
              <div className="text-center py-12">
                <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Quản Lý Đơn Hàng</h3>
                <p className="text-gray-500">Tính năng quản lý đơn hàng đang được phát triển</p>
              </div>
            </div>
          )}

          {/* Analytics Tab Content */}
          {activeTab === 'analytics' && (
            <div className="p-6">
              <div className="text-center py-12">
                <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Thống Kê Bán Hàng</h3>
                <p className="text-gray-500">Tính năng thống kê đang được phát triển</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FoodManagementPage;
